# Fitur Mutasi Promosi Demosi dengan Dependent Dropdown

## Overview
Fitur ini mengimplementasikan sistem mutasi, promosi, dan demosi karyawan dengan dependent dropdown yang menghubungkan Departemen dan Divisi. Ketika user memilih departemen, dropdown divisi akan otomatis difilter untuk menampilkan hanya divisi yang berada di bawah departemen tersebut.

## Fitur Utama

### 1. Dependent Dropdown Departemen → Divisi
- **Reactive Dropdown**: <PERSON><PERSON><PERSON> departemen dipilih, divisi otomatis difilter
- **Auto Reset**: Ketika departemen berubah, pilihan divisi akan direset ke null
- **Dynamic Options**: Dropdown divisi hanya menampilkan divisi yang sesuai dengan departemen yang dipilih

### 2. Form Mutasi Promosi Demosi
- **<PERSON><PERSON>**: <PERSON>mosi, <PERSON><PERSON><PERSON>, atau Mutasi
- **Tanggal Efektif**: <PERSON><PERSON> perubahan berlaku
- **Posisi Baru**: Entitas, Departemen, Divisi, dan <PERSON><PERSON><PERSON> baru
- **Status Aktif**: Opsi untuk langsung mengaktifkan posisi baru
- **Alasan**: Keterangan alasan perubahan

### 3. Validasi Data
- Memastikan divisi yang dipilih benar-benar berada di bawah departemen yang dipilih
- Validasi konsistensi data departemen-divisi
- Validasi field wajib

## Implementasi Teknis

### File yang Dimodifikasi

#### 1. MutasiPromosiDemosiRelationManager.php
```php
// Implementasi dependent dropdown
Select::make('departemen_id')
    ->label('Departemen')
    ->options(Departemen::pluck('nama_departemen', 'id'))
    ->searchable()
    ->required()
    ->reactive()
    ->afterStateUpdated(fn(callable $set) => $set('divisi_id', null))

Select::make('divisi_id')
    ->label('Divisi')
    ->options(function (Get $get) {
        $departemenId = $get('departemen_id');
        if (!$departemenId) {
            return [];
        }
        return Divisi::where('departemen_id', $departemenId)
            ->pluck('nama_divisi', 'id');
    })
    ->searchable()
    ->required()
```

### Cara Kerja Dependent Dropdown

1. **Reactive State**: Field `departemen_id` menggunakan `reactive()` untuk mendeteksi perubahan
2. **State Update**: Ketika departemen berubah, `afterStateUpdated()` mereset `divisi_id` ke null
3. **Dynamic Filtering**: Field `divisi_id` menggunakan closure dengan parameter `Get $get` untuk mendapatkan nilai departemen yang dipilih
4. **Conditional Options**: Jika tidak ada departemen yang dipilih, dropdown divisi mengembalikan array kosong

### Model Relationships

#### MutasiPromosiDemosi Model
```php
public function departemen()
{
    return $this->belongsTo(Departemen::class);
}

public function divisi()
{
    return $this->belongsTo(Divisi::class);
}
```

#### Departemen Model
```php
public function divisi()
{
    return $this->hasMany(Divisi::class, 'departemen_id');
}
```

#### Divisi Model
```php
public function departemen()
{
    return $this->belongsTo(Departemen::class, 'departemen_id');
}
```

## Testing

### Test Coverage
File test: `tests/Feature/MutasiPromosiDemosiDependentDropdownTest.php`

#### Test Cases:
1. **it_can_create_mutasi_with_correct_departemen_divisi_relationship**
   - Memastikan mutasi dapat dibuat dengan relasi departemen-divisi yang benar

2. **it_filters_divisi_by_departemen_correctly**
   - Memverifikasi filtering divisi berdasarkan departemen berfungsi dengan benar

3. **it_validates_departemen_divisi_consistency**
   - Memastikan konsistensi data antara departemen dan divisi

4. **it_can_create_mutasi_record_with_all_required_fields**
   - Memverifikasi pembuatan record mutasi dengan semua field yang diperlukan

### Menjalankan Test
```bash
php artisan test tests/Feature/MutasiPromosiDemosiDependentDropdownTest.php
```

## Penggunaan

### 1. Akses Fitur
- Buka halaman edit karyawan di admin panel
- Pilih tab "Riwayat Pekerjaan" (Relation Manager)
- Klik tombol "Tambah Posisi"

### 2. Mengisi Form
1. Pilih **Jenis Perubahan** (Promosi/Demosi/Mutasi)
2. Tentukan **Tanggal Efektif**
3. Pilih **Entitas** (lokasi kerja)
4. Pilih **Departemen** - dropdown divisi akan otomatis difilter
5. Pilih **Divisi** - hanya menampilkan divisi dari departemen yang dipilih
6. Pilih **Jabatan** baru
7. Isi **Alasan** perubahan
8. Centang **Jadikan Posisi Aktif Sekarang** jika ingin langsung mengaktifkan

### 3. Validasi Otomatis
- Sistem akan memvalidasi bahwa divisi yang dipilih sesuai dengan departemen
- Jika departemen diubah, pilihan divisi akan direset otomatis
- Form tidak dapat disubmit jika ada ketidakkonsistenan data

## Keuntungan Implementasi

### 1. User Experience
- **Intuitive**: User tidak perlu mengingat divisi mana yang berada di departemen mana
- **Error Prevention**: Mencegah kesalahan pemilihan divisi yang tidak sesuai departemen
- **Efficient**: Mengurangi jumlah opsi yang ditampilkan di dropdown divisi

### 2. Data Integrity
- **Consistent Data**: Memastikan konsistensi relasi departemen-divisi
- **Validation**: Validasi otomatis mencegah data yang tidak valid
- **Referential Integrity**: Menjaga integritas referensial database

### 3. Maintainability
- **Clean Code**: Implementasi menggunakan pattern yang sudah ada di codebase
- **Reusable**: Pattern ini dapat digunakan untuk dependent dropdown lainnya
- **Testable**: Mudah untuk ditest dan diverifikasi

## Best Practices yang Diterapkan

1. **Filament Patterns**: Menggunakan `reactive()` dan `afterStateUpdated()` sesuai dokumentasi Filament
2. **Clean Code**: Kode yang mudah dibaca dan dipahami
3. **SOLID Principles**: Implementasi yang mengikuti prinsip SOLID
4. **Testing**: Comprehensive test coverage untuk memastikan functionality
5. **Documentation**: Dokumentasi yang lengkap untuk maintenance

## Troubleshooting

### Issue: Dropdown divisi tidak ter-update ketika departemen berubah
**Solusi**: Pastikan field departemen menggunakan `reactive()` dan `afterStateUpdated()`

### Issue: Divisi dari departemen lain masih muncul
**Solusi**: Periksa query filtering di closure `options()` pada field divisi

### Issue: Form error ketika submit
**Solusi**: Pastikan semua field required terisi dan relasi departemen-divisi konsisten

## Future Enhancements

1. **Bulk Operations**: Implementasi mutasi massal untuk multiple karyawan
2. **Approval Workflow**: Sistem approval untuk mutasi yang memerlukan persetujuan
3. **History Tracking**: Tracking lengkap history perubahan posisi karyawan
4. **Notification**: Notifikasi otomatis ke karyawan dan supervisor terkait
5. **Reporting**: Report mutasi, promosi, demosi per periode
