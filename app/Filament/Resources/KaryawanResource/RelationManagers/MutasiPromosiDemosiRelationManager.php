<?php

namespace App\Filament\Resources\KaryawanResource\RelationManagers;

use App\Models\Entitas;
use App\Models\Departemen;
use App\Models\Divisi;
use App\Models\Jabatan;
use Filament\Forms\Form;
use Filament\Tables;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Columns\TextColumn;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Get;


class MutasiPromosiDemosiRelationManager extends RelationManager
{
    protected static string $relationship = 'mutasiPromosiDemosi';
    protected static ?string $title = 'Riwayat Pekerjaan';
    protected static ?string $recordTitleAttribute = 'tipe';

    public function form(Form $form): Form
    {
        return $form->schema([
            Select::make('tipe')
                ->label('<PERSON><PERSON>')
                ->options([
                    'promosi' => 'Promosi',
                    'demosi' => 'Demosi',
                    'mutasi' => 'Mutasi',
                ])
                ->required(),

            DatePicker::make('tanggal_efektif')
                ->label('Tanggal Efektif')
                ->required(),

            Toggle::make('is_active')
                ->dehydrated()
                ->label('Jadikan Posisi Aktif Sekarang'),

            Select::make('entitas_id')
                ->label('Entitas')
                ->options(Entitas::pluck('nama', 'id'))
                ->searchable()
                ->required()
                ->createOptionForm([
                    TextInput::make('nama')->label('Nama Entitas')->required(),
                    Textarea::make('alamat')->label('Alamat')->nullable(),
                    Textarea::make('keterangan')->label('Keterangan')->nullable(),
                ])
                ->createOptionUsing(fn(array $data) => Entitas::create($data)->id),

            Select::make('departemen_id')
                ->label('Departemen')
                ->options(Departemen::pluck('nama_departemen', 'id'))
                ->searchable()
                ->required()
                ->reactive()
                ->afterStateUpdated(fn(callable $set) => $set('divisi_id', null))
                ->createOptionForm([
                    TextInput::make('nama_departemen')->label('Nama Departemen')->required(),
                ])
                ->createOptionUsing(fn(array $data) => Departemen::create($data)->id),

            Select::make('divisi_id')
                ->label('Divisi')
                ->options(function (Get $get) {
                    $departemenId = $get('departemen_id');
                    if (!$departemenId) {
                        return [];
                    }
                    return Divisi::where('departemen_id', $departemenId)
                        ->pluck('nama_divisi', 'id');
                })
                ->searchable()
                ->required()
                ->createOptionForm([
                    Select::make('departemen_id')
                        ->label('Departemen')
                        ->options(Departemen::pluck('nama_departemen', 'id'))
                        ->searchable()
                        ->required(),
                    TextInput::make('nama_divisi')->label('Nama Divisi')->required(),
                    Textarea::make('deskripsi')->label('Deskripsi')->nullable(),
                ])
                ->createOptionUsing(fn(array $data) => Divisi::create($data)->id),

            Select::make('jabatan_id')
                ->label('Jabatan')
                ->options(Jabatan::pluck('nama_jabatan', 'id'))
                ->searchable()
                ->required()
                ->createOptionForm([
                    TextInput::make('nama_jabatan')->label('Nama Jabatan')->required(),
                    Textarea::make('deskripsi')->label('Deskripsi')->nullable(),
                ])
                ->createOptionUsing(fn(array $data) => Jabatan::create($data)->id),

            Textarea::make('alasan')->label('Alasan Perubahan')->rows(3),
        ]);
    }

    public function table(Tables\Table $table): Tables\Table
    {
        return $table
            ->columns([
                TextColumn::make('tipe')->label('Tipe'),
                TextColumn::make('entitas.nama')->label('Entitas'),
                TextColumn::make('departemen.nama_departemen')->label('Departemen'),
                TextColumn::make('divisi.nama_divisi')->label('Divisi'),
                TextColumn::make('jabatan.nama_jabatan')->label('Jabatan'),
                TextColumn::make('tanggal_efektif')->label('Efektif')->date(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('Aktif')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->color(fn(bool $state) => $state ? 'success' : 'gray'),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->label('Tambah Posisi')
                    ->after(function ($record) {
                        $record->karyawan->mutasiPromosiDemosi()
                            ->where('id', '!=', $record->id)
                            ->update(['is_active' => false]);

                        $record->update(['is_active' => true]);

                        $record->karyawan->update([
                            'id_entitas' => $record->entitas_id,
                            'id_departemen' => $record->departemen_id,
                            'id_divisi' => $record->divisi_id,
                            'id_jabatan' => $record->jabatan_id,
                        ]);
                    }),
            ]);
    }
}
