<?php

namespace Database\Factories;

use App\Models\Jabatan;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Jabatan>
 */
class JabatanFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Jabatan::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $positions = [
            'Manager',
            'Supervisor',
            'Staff',
            'Senior Staff',
            'Team Leader',
            'Assistant Manager',
            'Coordinator',
            'Analyst',
            'Specialist',
            'Executive'
        ];

        return [
            'nama_jabatan' => $this->faker->randomElement($positions) . ' ' . $this->faker->randomElement(['IT', 'HR', 'Finance', 'Marketing', 'Operations']),
            'deskripsi' => $this->faker->optional()->sentence(),
            'created_by' => 1,
        ];
    }

    /**
     * Indicate that the jabatan is a manager position.
     */
    public function manager(): static
    {
        return $this->state(fn(array $attributes) => [
            'nama_jabatan' => 'Manager ' . $this->faker->randomElement(['IT', 'HR', 'Finance', 'Marketing', 'Operations']),
            'deskripsi' => 'Manager level position with leadership responsibilities',
        ]);
    }

    /**
     * Indicate that the jabatan is a staff position.
     */
    public function staff(): static
    {
        return $this->state(fn(array $attributes) => [
            'nama_jabatan' => 'Staff ' . $this->faker->randomElement(['IT', 'HR', 'Finance', 'Marketing', 'Operations']),
            'deskripsi' => 'Staff level position with operational responsibilities',
        ]);
    }
}
